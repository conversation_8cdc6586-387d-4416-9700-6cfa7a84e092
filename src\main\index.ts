import { app, shell, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import { configManager } from './config/manager'
import { lifecycleManager } from './lifecycle/manager'
import { predefinedThemes } from './config/defaults'

let mainWindow: BrowserWindow | null = null

function createWindow(): void {
  const config = configManager.getConfig()

  // Create the browser window with configuration
  mainWindow = new BrowserWindow({
    width: config.window.width,
    height: config.window.height,
    minWidth: config.window.minWidth,
    minHeight: config.window.minHeight,
    show: false,
    autoHideMenuBar: true,
    resizable: config.window.resizable,
    maximizable: config.window.maximizable,
    minimizable: config.window.minimizable,
    closable: config.window.closable,
    alwaysOnTop: config.window.alwaysOnTop,
    skipTaskbar: config.window.skipTaskbar,
    titleBarStyle: config.window.titleBarStyle,
    frame: config.window.frame,
    transparent: config.window.transparent,
    opacity: config.window.opacity,
    vibrancy: config.window.vibrancy,
    backgroundMaterial: config.window.backgroundMaterial,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    if (mainWindow) {
      if (config.startup.startMinimized) {
        mainWindow.minimize()
      } else if (!config.startup.startInTray) {
        mainWindow.show()
      }
    }
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // Initialize lifecycle manager with the main window
  lifecycleManager.initialize(mainWindow)
}

// Setup IPC handlers
function setupIpcHandlers(): void {
  // Configuration IPC handlers
  ipcMain.handle('get-config', () => {
    return configManager.getConfig()
  })

  ipcMain.handle('update-config', async (_, updates) => {
    await configManager.updateConfig(updates)
    return configManager.getConfig()
  })

  ipcMain.handle('reset-config', async () => {
    await configManager.resetToDefaults()
    return configManager.getConfig()
  })

  ipcMain.handle('export-config', async (_, filePath) => {
    await configManager.exportConfig(filePath)
  })

  ipcMain.handle('import-config', async (_, filePath) => {
    await configManager.importConfig(filePath)
    return configManager.getConfig()
  })

  // Theme IPC handlers
  ipcMain.handle('get-available-themes', () => {
    return predefinedThemes
  })

  ipcMain.handle('apply-theme', async (_, theme) => {
    await configManager.updateConfig({ theme })
    if (mainWindow) {
      mainWindow.webContents.send('theme-changed', theme)
    }
  })

  // Dialog IPC handlers
  ipcMain.handle('show-message-box', async (_, options) => {
    if (mainWindow) {
      return await dialog.showMessageBox(mainWindow, options)
    }
    return await dialog.showMessageBox(options)
  })

  // Legacy IPC handlers
  ipcMain.on('ping', () => console.log('pong'))
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron.modernapp')

  // Initialize configuration manager
  await configManager.initialize()

  // Setup IPC handlers
  setupIpcHandlers()

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })

  // Handle create-window event from lifecycle manager
  app.on('create-window' as any, () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
