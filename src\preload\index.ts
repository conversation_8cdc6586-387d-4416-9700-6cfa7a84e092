import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// Custom APIs for renderer
const api = {
  // Configuration APIs
  getConfig: () => ipcRenderer.invoke('get-config'),
  updateConfig: (updates: any) => ipcRenderer.invoke('update-config', updates),
  resetConfig: () => ipcRenderer.invoke('reset-config'),
  exportConfig: (filePath: string) => ipcRenderer.invoke('export-config', filePath),
  importConfig: (filePath: string) => ipcRenderer.invoke('import-config', filePath),

  // Theme APIs
  getAvailableThemes: () => ipcRenderer.invoke('get-available-themes'),
  applyTheme: (theme: any) => ipcRenderer.invoke('apply-theme', theme),

  // Dialog APIs
  showMessageBox: (options: any) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options: any) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options: any) => ipcRenderer.invoke('show-open-dialog', options),

  // Event listeners
  onThemeChanged: (callback: (theme: any) => void) => {
    ipcRenderer.on('theme-changed', (_, theme) => callback(theme))
  },
  onOpenSettings: (callback: () => void) => {
    ipcRenderer.on('open-settings', callback)
  },

  // Remove listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel)
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
