<template>
  <div class="theme-settings">
    <div class="section-header">
      <h2 class="text-xl font-semibold">Appearance & Theming</h2>
      <p class="text-secondary">Customize the look and feel of your application</p>
    </div>

    <!-- Theme Selection -->
    <div class="setting-group">
      <h3 class="setting-title">Theme</h3>
      <p class="setting-description">Choose from predefined themes or create your own</p>
      
      <div class="theme-grid">
        <div
          v-for="(theme, name) in availableThemes"
          :key="name"
          @click="selectTheme(name)"
          :class="[
            'theme-card',
            { 'theme-card-active': currentTheme === name }
          ]"
        >
          <div class="theme-preview" :style="getThemePreviewStyle(theme)">
            <div class="preview-header" :style="{ backgroundColor: theme.colors.primary }"></div>
            <div class="preview-content">
              <div class="preview-text" :style="{ color: theme.colors.text }">{{ theme.name }}</div>
              <div class="preview-button" :style="{ backgroundColor: theme.colors.accent }"></div>
            </div>
          </div>
          <div class="theme-info">
            <span class="theme-name">{{ theme.name }}</span>
            <span v-if="currentTheme === name" class="theme-active-badge">Active</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Dark Mode Toggle -->
    <div class="setting-group">
      <div class="setting-row">
        <div class="setting-info">
          <h3 class="setting-title">Dark Mode</h3>
          <p class="setting-description">Toggle between light and dark themes</p>
        </div>
        <label class="toggle-switch">
          <input
            type="checkbox"
            v-model="isDarkMode"
            @change="toggleDarkMode"
          />
          <span class="toggle-slider"></span>
        </label>
      </div>
    </div>

    <!-- System Theme -->
    <div class="setting-group">
      <div class="setting-row">
        <div class="setting-info">
          <h3 class="setting-title">Follow System Theme</h3>
          <p class="setting-description">Automatically switch theme based on system preference</p>
        </div>
        <label class="toggle-switch">
          <input
            type="checkbox"
            v-model="followSystemTheme"
            @change="updateFollowSystemTheme"
          />
          <span class="toggle-slider"></span>
        </label>
      </div>
    </div>

    <!-- Custom Theme Builder -->
    <div class="setting-group">
      <h3 class="setting-title">Custom Theme</h3>
      <p class="setting-description">Create your own theme by customizing colors and typography</p>
      
      <div class="custom-theme-builder" v-if="showCustomBuilder">
        <!-- Color Customization -->
        <div class="color-section">
          <h4 class="subsection-title">Colors</h4>
          <div class="color-grid">
            <div v-for="(color, key) in customTheme.colors" :key="key" class="color-input-group">
              <label :for="`color-${key}`" class="color-label">{{ formatColorLabel(key) }}</label>
              <div class="color-input-wrapper">
                <input
                  :id="`color-${key}`"
                  type="color"
                  v-model="customTheme.colors[key]"
                  class="color-input"
                />
                <input
                  type="text"
                  v-model="customTheme.colors[key]"
                  class="color-text-input"
                  @input="validateColorInput"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Typography Customization -->
        <div class="typography-section">
          <h4 class="subsection-title">Typography</h4>
          
          <div class="typography-controls">
            <div class="control-group">
              <label for="font-family" class="control-label">Font Family</label>
              <select id="font-family" v-model="customTheme.typography.fontFamily" class="control-select">
                <option value="Inter, sans-serif">Inter</option>
                <option value="Roboto, sans-serif">Roboto</option>
                <option value="Open Sans, sans-serif">Open Sans</option>
                <option value="Lato, sans-serif">Lato</option>
                <option value="Montserrat, sans-serif">Montserrat</option>
                <option value="Poppins, sans-serif">Poppins</option>
                <option value="system-ui, sans-serif">System Default</option>
              </select>
            </div>

            <div class="font-size-controls">
              <div v-for="(size, key) in customTheme.typography.fontSize" :key="key" class="control-group">
                <label :for="`font-size-${key}`" class="control-label">{{ formatSizeLabel(key) }}</label>
                <input
                  :id="`font-size-${key}`"
                  type="text"
                  v-model="customTheme.typography.fontSize[key]"
                  class="control-input"
                  placeholder="e.g., 1rem, 16px"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Theme Actions -->
        <div class="theme-actions">
          <button @click="previewCustomTheme" class="btn btn-outline">
            Preview Theme
          </button>
          <button @click="saveCustomTheme" class="btn btn-primary">
            Save Custom Theme
          </button>
          <button @click="resetCustomTheme" class="btn btn-secondary">
            Reset
          </button>
        </div>
      </div>

      <button
        @click="showCustomBuilder = !showCustomBuilder"
        class="btn btn-outline"
      >
        {{ showCustomBuilder ? 'Hide' : 'Show' }} Custom Theme Builder
      </button>
    </div>

    <!-- Theme Import/Export -->
    <div class="setting-group">
      <h3 class="setting-title">Theme Management</h3>
      <p class="setting-description">Import and export theme configurations</p>
      
      <div class="theme-management-actions">
        <button @click="exportCurrentTheme" class="btn btn-outline">
          Export Current Theme
        </button>
        <button @click="importTheme" class="btn btn-outline">
          Import Theme
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useTheme } from '../../composables/useTheme';
import { useNotifications } from '../../composables/useNotifications';
import { ThemeConfig } from '../../../../main/config/types';

const {
  themeState,
  currentThemeConfig,
  themeList,
  isDarkMode: themeIsDarkMode,
  switchTheme,
  createCustomTheme,
  exportTheme,
  importTheme: importThemeFile,
  toggleDarkMode: themeToggleDarkMode
} = useTheme();

const { showNotification } = useNotifications();

const showCustomBuilder = ref(false);
const followSystemTheme = ref(false);
const customTheme = ref<ThemeConfig>({
  name: 'Custom Theme',
  colors: {
    primary: '#6366f1',
    secondary: '#8b5cf6',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#0f172a',
    textSecondary: '#475569',
    accent: '#06b6d4',
    error: '#ef4444',
    warning: '#f59e0b',
    success: '#10b981'
  },
  typography: {
    fontFamily: 'Inter, sans-serif',
    fontSize: {
      small: '0.875rem',
      medium: '1rem',
      large: '1.125rem',
      xlarge: '1.25rem'
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      bold: 700
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  },
  borderRadius: {
    small: '0.25rem',
    medium: '0.5rem',
    large: '0.75rem'
  },
  shadows: {
    small: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    medium: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    large: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
  }
});

const availableThemes = computed(() => themeState.value.availableThemes);
const currentTheme = computed(() => themeState.value.currentTheme);
const isDarkMode = computed({
  get: () => themeIsDarkMode.value,
  set: (value) => {
    if (value !== themeIsDarkMode.value) {
      themeToggleDarkMode();
    }
  }
});

const selectTheme = async (themeName: string) => {
  try {
    await switchTheme(themeName);
    showNotification(`Switched to ${themeName} theme`, 'success');
  } catch (error) {
    showNotification('Failed to switch theme', 'error');
    console.error('Failed to switch theme:', error);
  }
};

const toggleDarkMode = async () => {
  try {
    await themeToggleDarkMode();
  } catch (error) {
    showNotification('Failed to toggle dark mode', 'error');
    console.error('Failed to toggle dark mode:', error);
  }
};

const updateFollowSystemTheme = async () => {
  try {
    // Implement system theme following logic
    if (followSystemTheme.value) {
      // Enable system theme detection
      showNotification('Now following system theme', 'success');
    } else {
      showNotification('Stopped following system theme', 'info');
    }
  } catch (error) {
    showNotification('Failed to update system theme setting', 'error');
  }
};

const previewCustomTheme = async () => {
  try {
    await createCustomTheme(customTheme.value);
    showNotification('Custom theme preview applied', 'success');
  } catch (error) {
    showNotification('Failed to preview custom theme', 'error');
    console.error('Failed to preview custom theme:', error);
  }
};

const saveCustomTheme = async () => {
  try {
    await createCustomTheme(customTheme.value);
    showNotification('Custom theme saved successfully', 'success');
  } catch (error) {
    showNotification('Failed to save custom theme', 'error');
    console.error('Failed to save custom theme:', error);
  }
};

const resetCustomTheme = () => {
  // Reset to current theme configuration
  if (currentThemeConfig.value) {
    customTheme.value = { ...currentThemeConfig.value };
  }
  showNotification('Custom theme reset', 'info');
};

const exportCurrentTheme = async () => {
  try {
    const theme = await exportTheme();
    if (theme) {
      // Trigger download
      const blob = new Blob([JSON.stringify(theme, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${theme.name.toLowerCase().replace(/\s+/g, '-')}-theme.json`;
      a.click();
      URL.revokeObjectURL(url);
      showNotification('Theme exported successfully', 'success');
    }
  } catch (error) {
    showNotification('Failed to export theme', 'error');
    console.error('Failed to export theme:', error);
  }
};

const importTheme = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  input.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      try {
        const text = await file.text();
        const theme = JSON.parse(text) as ThemeConfig;
        await importThemeFile(theme);
        showNotification('Theme imported successfully', 'success');
      } catch (error) {
        showNotification('Failed to import theme', 'error');
        console.error('Failed to import theme:', error);
      }
    }
  };
  input.click();
};

const getThemePreviewStyle = (theme: ThemeConfig) => {
  return {
    backgroundColor: theme.colors.background,
    border: `1px solid ${theme.colors.textSecondary}20`
  };
};

const formatColorLabel = (key: string): string => {
  return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
};

const formatSizeLabel = (key: string): string => {
  return key.charAt(0).toUpperCase() + key.slice(1);
};

const validateColorInput = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const value = input.value;
  
  // Basic hex color validation
  if (!/^#[0-9A-Fa-f]{6}$/.test(value)) {
    input.setCustomValidity('Please enter a valid hex color (e.g., #ff0000)');
  } else {
    input.setCustomValidity('');
  }
};

// Initialize custom theme with current theme
watch(currentThemeConfig, (newTheme) => {
  if (newTheme) {
    customTheme.value = { ...newTheme };
  }
}, { immediate: true });

onMounted(() => {
  // Load theme settings
});
</script>

<style scoped>
.theme-settings {
  space-y: var(--spacing-8);
}

.section-header {
  margin-bottom: var(--spacing-6);
}

.setting-group {
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-6);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
}

.setting-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-2);
}

.setting-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setting-info {
  flex: 1;
}

/* Theme Grid */
.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-top: var(--spacing-4);
}

.theme-card {
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition-all);
}

.theme-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.theme-card-active {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-lg);
}

.theme-preview {
  height: 100px;
  position: relative;
  overflow: hidden;
}

.preview-header {
  height: 20px;
  width: 100%;
}

.preview-content {
  padding: var(--spacing-3);
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.preview-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.preview-button {
  width: 60px;
  height: 20px;
  border-radius: var(--radius-sm);
}

.theme-info {
  padding: var(--spacing-3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-surface-secondary);
}

.theme-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.theme-active-badge {
  font-size: var(--font-size-xs);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: var(--radius-full);
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-gray-300);
  transition: var(--transition-fast);
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-fast);
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--color-primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Custom Theme Builder */
.custom-theme-builder {
  margin-top: var(--spacing-6);
  padding: var(--spacing-6);
  background-color: var(--color-background-secondary);
  border-radius: var(--radius-md);
}

.subsection-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.color-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.color-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.color-input-wrapper {
  display: flex;
  gap: var(--spacing-2);
}

.color-input {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
}

.color-text-input {
  flex: 1;
  padding: var(--spacing-2);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-family: var(--font-family-secondary);
}

.typography-controls {
  display: grid;
  gap: var(--spacing-4);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.control-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.control-select,
.control-input {
  padding: var(--spacing-2);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background-color: var(--color-surface);
}

.font-size-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-4);
}

.theme-actions {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-6);
}

.theme-management-actions {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-4);
}

/* Responsive */
@media (max-width: 768px) {
  .theme-grid {
    grid-template-columns: 1fr;
  }
  
  .color-grid {
    grid-template-columns: 1fr;
  }
  
  .theme-actions,
  .theme-management-actions {
    flex-direction: column;
  }
}
</style>
