<template>
  <div class="settings-panel">
    <!-- Settings Header -->
    <div class="settings-header">
      <h1 class="text-2xl font-bold text-primary">Settings</h1>
      <p class="text-secondary">Customize your application experience</p>
    </div>

    <!-- Settings Navigation -->
    <div class="settings-layout">
      <nav class="settings-nav">
        <ul class="nav-list">
          <li v-for="section in settingSections" :key="section.id">
            <button
              @click="activeSection = section.id"
              :class="[
                'nav-item',
                { 'nav-item-active': activeSection === section.id }
              ]"
            >
              <component :is="section.icon" class="nav-icon" />
              <span>{{ section.title }}</span>
            </button>
          </li>
        </ul>
      </nav>

      <!-- Settings Content -->
      <div class="settings-content">
        <div class="content-wrapper">
          <!-- General Settings -->
          <GeneralSettings v-if="activeSection === 'general'" />
          
          <!-- Theme Settings -->
          <ThemeSettings v-if="activeSection === 'theme'" />
          
          <!-- Window Settings -->
          <WindowSettings v-if="activeSection === 'window'" />
          
          <!-- Startup Settings -->
          <StartupSettings v-if="activeSection === 'startup'" />
          
          <!-- System Tray Settings -->
          <TraySettings v-if="activeSection === 'tray'" />
          
          <!-- Registry Settings -->
          <RegistrySettings v-if="activeSection === 'registry'" />
          
          <!-- Advanced Settings -->
          <AdvancedSettings v-if="activeSection === 'advanced'" />
        </div>
      </div>
    </div>

    <!-- Settings Footer -->
    <div class="settings-footer">
      <div class="footer-actions">
        <button @click="resetToDefaults" class="btn btn-secondary">
          Reset to Defaults
        </button>
        <button @click="exportSettings" class="btn btn-outline">
          Export Settings
        </button>
        <button @click="importSettings" class="btn btn-outline">
          Import Settings
        </button>
        <button @click="saveSettings" class="btn btn-primary">
          Save Changes
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useSettings } from '../../composables/useSettings';
import GeneralSettings from './GeneralSettings.vue';
import ThemeSettings from './ThemeSettings.vue';
import WindowSettings from './WindowSettings.vue';
import StartupSettings from './StartupSettings.vue';
import TraySettings from './TraySettings.vue';
import RegistrySettings from './RegistrySettings.vue';
import AdvancedSettings from './AdvancedSettings.vue';

// Icons (you can replace these with your preferred icon library)
const SettingsIcon = { template: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path></svg>' };
const PaletteIcon = { template: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a2 2 0 002 2h2a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 0a2 2 0 00-2 2v7a2 2 0 002 2h2a2 2 0 002-2V4a2 2 0 00-2-2h-2zm6 0a2 2 0 00-2 2v11a2 2 0 002 2h2a2 2 0 002-2V4a2 2 0 00-2-2h-2z" clip-rule="evenodd"></path></svg>' };
const WindowIcon = { template: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v8a1 1 0 01-1 1H4a1 1 0 01-1-1V8z" clip-rule="evenodd"></path></svg>' };
const PlayIcon = { template: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg>' };
const TrayIcon = { template: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>' };
const DatabaseIcon = { template: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z"></path><path d="M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z"></path><path d="M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z"></path></svg>' };
const CogIcon = { template: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path></svg>' };

const activeSection = ref('general');
const { saveSettings, resetToDefaults, exportSettings, importSettings } = useSettings();

const settingSections = [
  { id: 'general', title: 'General', icon: SettingsIcon },
  { id: 'theme', title: 'Appearance', icon: PaletteIcon },
  { id: 'window', title: 'Window', icon: WindowIcon },
  { id: 'startup', title: 'Startup', icon: PlayIcon },
  { id: 'tray', title: 'System Tray', icon: TrayIcon },
  { id: 'registry', title: 'Registry', icon: DatabaseIcon },
  { id: 'advanced', title: 'Advanced', icon: CogIcon }
];

onMounted(() => {
  // Load settings on mount
});
</script>

<style scoped>
.settings-panel {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-background);
}

.settings-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-surface);
}

.settings-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.settings-nav {
  width: var(--sidebar-width);
  background-color: var(--color-surface-secondary);
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
}

.nav-list {
  list-style: none;
  padding: var(--spacing-4);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-colors);
  text-align: left;
}

.nav-item:hover {
  background-color: var(--color-surface-tertiary);
  color: var(--color-text);
}

.nav-item-active {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.nav-item-active:hover {
  background-color: var(--color-primary-hover);
}

.nav-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.settings-content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--color-background);
}

.content-wrapper {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-6);
}

.settings-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--color-border);
  background-color: var(--color-surface);
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  max-width: 800px;
  margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-layout {
    flex-direction: column;
  }
  
  .settings-nav {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--color-border);
  }
  
  .nav-list {
    display: flex;
    overflow-x: auto;
    padding: var(--spacing-2) var(--spacing-4);
  }
  
  .nav-item {
    white-space: nowrap;
    margin-right: var(--spacing-2);
    margin-bottom: 0;
  }
}
</style>
