import { ref, computed } from 'vue';
import { AppConfig } from '../../../main/config/types';

/**
 * Configuration management composable
 * Handles loading, updating, and managing application configuration
 */

const config = ref<AppConfig | null>(null);
const originalConfig = ref<AppConfig | null>(null);
const isLoading = ref(false);
const error = ref<string | null>(null);

export function useConfig() {
  /**
   * Load configuration from main process
   */
  const loadConfig = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const loadedConfig = await window.electron.ipcRenderer.invoke('get-config');
      config.value = loadedConfig;
      originalConfig.value = JSON.parse(JSON.stringify(loadedConfig)); // Deep clone
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load configuration';
      console.error('Failed to load configuration:', err);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Update configuration
   */
  const updateConfig = async (updates?: Partial<AppConfig>): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;

      const configToUpdate = updates || config.value;
      if (!configToUpdate) {
        throw new Error('No configuration to update');
      }

      const updatedConfig = await window.electron.ipcRenderer.invoke('update-config', configToUpdate);
      config.value = updatedConfig;
      originalConfig.value = JSON.parse(JSON.stringify(updatedConfig)); // Deep clone
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update configuration';
      console.error('Failed to update configuration:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Reset configuration to defaults
   */
  const resetConfig = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;

      const resetConfig = await window.electron.ipcRenderer.invoke('reset-config');
      config.value = resetConfig;
      originalConfig.value = JSON.parse(JSON.stringify(resetConfig)); // Deep clone
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to reset configuration';
      console.error('Failed to reset configuration:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Export configuration to file
   */
  const exportConfig = async (filePath?: string): Promise<void> => {
    try {
      if (!filePath) {
        // Show save dialog
        const result = await window.electron.ipcRenderer.invoke('show-save-dialog', {
          title: 'Export Configuration',
          defaultPath: 'app-config.yaml',
          filters: [
            { name: 'YAML Files', extensions: ['yaml', 'yml'] },
            { name: 'JSON Files', extensions: ['json'] },
            { name: 'All Files', extensions: ['*'] }
          ]
        });

        if (result.canceled || !result.filePath) {
          return;
        }

        filePath = result.filePath;
      }

      await window.electron.ipcRenderer.invoke('export-config', filePath);
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to export configuration';
      console.error('Failed to export configuration:', err);
      throw err;
    }
  };

  /**
   * Import configuration from file
   */
  const importConfig = async (filePath?: string): Promise<void> => {
    try {
      if (!filePath) {
        // Show open dialog
        const result = await window.electron.ipcRenderer.invoke('show-open-dialog', {
          title: 'Import Configuration',
          filters: [
            { name: 'YAML Files', extensions: ['yaml', 'yml'] },
            { name: 'JSON Files', extensions: ['json'] },
            { name: 'All Files', extensions: ['*'] }
          ],
          properties: ['openFile']
        });

        if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
          return;
        }

        filePath = result.filePaths[0];
      }

      const importedConfig = await window.electron.ipcRenderer.invoke('import-config', filePath);
      config.value = importedConfig;
      originalConfig.value = JSON.parse(JSON.stringify(importedConfig)); // Deep clone
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to import configuration';
      console.error('Failed to import configuration:', err);
      throw err;
    }
  };

  /**
   * Update a specific section of the configuration
   */
  const updateConfigSection = <K extends keyof AppConfig>(
    section: K,
    updates: Partial<AppConfig[K]>
  ): void => {
    if (!config.value) return;

    config.value = {
      ...config.value,
      [section]: {
        ...config.value[section],
        ...updates
      }
    };
  };

  /**
   * Get a specific section of the configuration
   */
  const getConfigSection = <K extends keyof AppConfig>(section: K): AppConfig[K] | null => {
    return config.value?.[section] || null;
  };

  /**
   * Check if configuration has unsaved changes
   */
  const hasUnsavedChanges = computed(() => {
    if (!config.value || !originalConfig.value) return false;
    return JSON.stringify(config.value) !== JSON.stringify(originalConfig.value);
  });

  /**
   * Revert configuration to last saved state
   */
  const revertChanges = (): void => {
    if (originalConfig.value) {
      config.value = JSON.parse(JSON.stringify(originalConfig.value)); // Deep clone
    }
  };

  /**
   * Validate configuration
   */
  const validateConfig = (configToValidate?: AppConfig): boolean => {
    const targetConfig = configToValidate || config.value;
    if (!targetConfig) return false;

    // Basic validation - you can extend this
    return (
      typeof targetConfig.version === 'string' &&
      typeof targetConfig.theme === 'object' &&
      typeof targetConfig.window === 'object' &&
      typeof targetConfig.startup === 'object' &&
      typeof targetConfig.tray === 'object' &&
      typeof targetConfig.registry === 'object' &&
      typeof targetConfig.general === 'object'
    );
  };

  return {
    // State
    config: readonly(config),
    originalConfig: readonly(originalConfig),
    isLoading: readonly(isLoading),
    error: readonly(error),
    hasUnsavedChanges,

    // Methods
    loadConfig,
    updateConfig,
    resetConfig,
    exportConfig,
    importConfig,
    updateConfigSection,
    getConfigSection,
    revertChanges,
    validateConfig
  };
}

// Global configuration state
const globalConfigState = useConfig();

// Auto-load configuration on first use
let configLoaded = false;
export const useGlobalConfig = () => {
  if (!configLoaded) {
    globalConfigState.loadConfig();
    configLoaded = true;
  }
  return globalConfigState;
};
