import { app, BrowserWindow, Tray, Menu, nativeImage, dialog, shell } from 'electron';
import * as path from 'path';
import { configManager } from '../config/manager';
import { AppConfig } from '../config/types';

/**
 * Application Lifecycle Manager
 * Handles auto-startup, single instance, system tray, and startup behavior
 */
export class LifecycleManager {
  private tray: Tray | null = null;
  private mainWindow: BrowserWindow | null = null;
  private isQuitting = false;
  private autoLauncher: any = null; // Will be dynamically imported

  constructor() {
    this.setupSingleInstance();
    this.setupAppEvents();
  }

  /**
   * Initialize the lifecycle manager
   */
  async initialize(mainWindow: BrowserWindow): Promise<void> {
    this.mainWindow = mainWindow;
    
    try {
      // Initialize auto-launcher
      await this.initializeAutoLauncher();
      
      // Setup system tray
      await this.setupSystemTray();
      
      // Apply startup configuration
      await this.applyStartupConfig();
      
      // Setup window events
      this.setupWindowEvents();
      
      // Watch for configuration changes
      configManager.watch('lifecycle', this.onConfigChanged.bind(this));
    } catch (error) {
      console.error('Failed to initialize lifecycle manager:', error);
    }
  }

  /**
   * Setup single instance enforcement
   */
  private setupSingleInstance(): void {
    const gotTheLock = app.requestSingleInstanceLock();

    if (!gotTheLock) {
      app.quit();
      return;
    }

    app.on('second-instance', (event, commandLine, workingDirectory) => {
      // Someone tried to run a second instance, focus our window instead
      if (this.mainWindow) {
        if (this.mainWindow.isMinimized()) {
          this.mainWindow.restore();
        }
        if (!this.mainWindow.isVisible()) {
          this.mainWindow.show();
        }
        this.mainWindow.focus();
      }
    });
  }

  /**
   * Setup application events
   */
  private setupAppEvents(): void {
    app.on('before-quit', () => {
      this.isQuitting = true;
    });

    app.on('activate', () => {
      // On macOS, re-create window when dock icon is clicked
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      } else if (this.mainWindow) {
        this.mainWindow.show();
      }
    });

    app.on('window-all-closed', () => {
      const config = configManager.getConfig();
      
      // On macOS, keep app running when all windows are closed
      if (process.platform === 'darwin') {
        return;
      }
      
      // If close to tray is enabled, don't quit
      if (config.tray.enabled && config.tray.closeToTray) {
        return;
      }
      
      app.quit();
    });
  }

  /**
   * Initialize auto-launcher
   */
  private async initializeAutoLauncher(): Promise<void> {
    try {
      // Dynamically import auto-launch to avoid issues on non-supported platforms
      const AutoLaunch = require('auto-launch');
      
      this.autoLauncher = new AutoLaunch({
        name: app.getName(),
        path: process.execPath,
        isHidden: false // Will be set based on config
      });
    } catch (error) {
      console.warn('Auto-launch not available:', error);
    }
  }

  /**
   * Setup system tray
   */
  private async setupSystemTray(): Promise<void> {
    const config = configManager.getConfig();
    
    if (!config.tray.enabled) {
      if (this.tray) {
        this.tray.destroy();
        this.tray = null;
      }
      return;
    }

    try {
      // Create tray icon
      const iconPath = this.getTrayIconPath();
      const trayIcon = nativeImage.createFromPath(iconPath);
      
      if (this.tray) {
        this.tray.destroy();
      }
      
      this.tray = new Tray(trayIcon);
      this.tray.setToolTip(config.tray.tooltip);
      
      // Setup tray context menu
      this.updateTrayMenu();
      
      // Handle tray click events
      this.tray.on('click', () => {
        if (this.mainWindow) {
          if (this.mainWindow.isVisible()) {
            this.mainWindow.hide();
          } else {
            this.mainWindow.show();
            this.mainWindow.focus();
          }
        }
      });

      this.tray.on('double-click', () => {
        if (this.mainWindow) {
          this.mainWindow.show();
          this.mainWindow.focus();
        }
      });
    } catch (error) {
      console.error('Failed to setup system tray:', error);
    }
  }

  /**
   * Update tray context menu
   */
  private updateTrayMenu(): void {
    if (!this.tray) return;

    const config = configManager.getConfig();
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Show App',
        click: () => {
          if (this.mainWindow) {
            this.mainWindow.show();
            this.mainWindow.focus();
          }
        }
      },
      {
        label: 'Hide App',
        click: () => {
          if (this.mainWindow) {
            this.mainWindow.hide();
          }
        }
      },
      { type: 'separator' },
      {
        label: 'Settings',
        click: () => {
          if (this.mainWindow) {
            this.mainWindow.show();
            this.mainWindow.focus();
            // Send message to renderer to open settings
            this.mainWindow.webContents.send('open-settings');
          }
        }
      },
      { type: 'separator' },
      {
        label: 'About',
        click: () => {
          dialog.showMessageBox({
            type: 'info',
            title: 'About',
            message: app.getName(),
            detail: `Version: ${app.getVersion()}\nElectron: ${process.versions.electron}\nNode: ${process.versions.node}`
          });
        }
      },
      {
        label: 'Check for Updates',
        click: () => {
          // Implement update check
          shell.openExternal('https://github.com/your-repo/releases');
        }
      },
      { type: 'separator' },
      {
        label: 'Quit',
        click: () => {
          this.isQuitting = true;
          app.quit();
        }
      }
    ]);

    this.tray.setContextMenu(contextMenu);
  }

  /**
   * Setup window events
   */
  private setupWindowEvents(): void {
    if (!this.mainWindow) return;

    const config = configManager.getConfig();

    // Handle window close
    this.mainWindow.on('close', (event) => {
      if (!this.isQuitting && config.tray.enabled && config.tray.closeToTray) {
        event.preventDefault();
        this.mainWindow?.hide();
        
        if (config.tray.showNotifications) {
          this.showTrayNotification('App minimized to tray', 'Click the tray icon to restore the window.');
        }
      }
    });

    // Handle window minimize
    this.mainWindow.on('minimize', (event) => {
      if (config.tray.enabled && config.tray.minimizeToTray) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });

    // Handle window show/hide
    this.mainWindow.on('show', () => {
      this.updateTrayMenu();
    });

    this.mainWindow.on('hide', () => {
      this.updateTrayMenu();
    });
  }

  /**
   * Apply startup configuration
   */
  private async applyStartupConfig(): Promise<void> {
    const config = configManager.getConfig();

    try {
      // Configure auto-startup
      if (this.autoLauncher) {
        const isEnabled = await this.autoLauncher.isEnabled();
        
        if (config.startup.autoStart && !isEnabled) {
          await this.autoLauncher.enable();
        } else if (!config.startup.autoStart && isEnabled) {
          await this.autoLauncher.disable();
        }
      }

      // Apply startup behavior
      if (this.mainWindow) {
        if (config.startup.startMinimized) {
          this.mainWindow.minimize();
        }

        if (config.startup.startInTray && config.tray.enabled) {
          this.mainWindow.hide();
        }
      }
    } catch (error) {
      console.error('Failed to apply startup configuration:', error);
    }
  }

  /**
   * Handle configuration changes
   */
  private async onConfigChanged(config: AppConfig): Promise<void> {
    try {
      // Update system tray
      await this.setupSystemTray();
      
      // Update auto-startup
      if (this.autoLauncher) {
        const isEnabled = await this.autoLauncher.isEnabled();
        
        if (config.startup.autoStart && !isEnabled) {
          await this.autoLauncher.enable();
        } else if (!config.startup.autoStart && isEnabled) {
          await this.autoLauncher.disable();
        }
      }
    } catch (error) {
      console.error('Failed to apply configuration changes:', error);
    }
  }

  /**
   * Get tray icon path
   */
  private getTrayIconPath(): string {
    const iconName = process.platform === 'win32' ? 'tray-icon.ico' : 'tray-icon.png';
    return path.join(__dirname, '../../resources', iconName);
  }

  /**
   * Show tray notification
   */
  private showTrayNotification(title: string, body: string): void {
    if (this.tray && process.platform === 'win32') {
      this.tray.displayBalloon({
        title,
        content: body,
        iconType: 'info'
      });
    }
  }

  /**
   * Create main window (for use when app is activated)
   */
  private createWindow(): void {
    // This should be implemented by the main process
    // For now, just emit an event
    app.emit('create-window' as any);
  }

  /**
   * Toggle auto-startup
   */
  async toggleAutoStartup(): Promise<boolean> {
    if (!this.autoLauncher) {
      throw new Error('Auto-launch not available');
    }

    try {
      const isEnabled = await this.autoLauncher.isEnabled();
      
      if (isEnabled) {
        await this.autoLauncher.disable();
        return false;
      } else {
        await this.autoLauncher.enable();
        return true;
      }
    } catch (error) {
      console.error('Failed to toggle auto-startup:', error);
      throw error;
    }
  }

  /**
   * Check if auto-startup is enabled
   */
  async isAutoStartupEnabled(): Promise<boolean> {
    if (!this.autoLauncher) {
      return false;
    }

    try {
      return await this.autoLauncher.isEnabled();
    } catch (error) {
      console.error('Failed to check auto-startup status:', error);
      return false;
    }
  }

  /**
   * Show main window
   */
  showMainWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.show();
      this.mainWindow.focus();
    }
  }

  /**
   * Hide main window
   */
  hideMainWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.hide();
    }
  }

  /**
   * Quit application
   */
  quitApplication(): void {
    this.isQuitting = true;
    app.quit();
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.tray) {
      this.tray.destroy();
      this.tray = null;
    }
    
    configManager.unwatch('lifecycle');
  }
}

// Singleton instance
export const lifecycleManager = new LifecycleManager();
