import { ref, computed, watch, onMounted } from 'vue';
import { ThemeConfig } from '../../../main/config/types';

/**
 * Theme management composable
 * Handles theme switching, CSS custom properties, and theme persistence
 */

export interface ThemeState {
  currentTheme: string;
  availableThemes: Record<string, ThemeConfig>;
  isDark: boolean;
  isHighContrast: boolean;
  customTheme: ThemeConfig | null;
}

const themeState = ref<ThemeState>({
  currentTheme: 'modern-dark',
  availableThemes: {},
  isDark: true,
  isHighContrast: false,
  customTheme: null
});

export function useTheme() {
  /**
   * Initialize theme system
   */
  const initializeTheme = async () => {
    try {
      // Load available themes from main process
      const themes = await window.electron.ipcRenderer.invoke('get-available-themes');
      themeState.value.availableThemes = themes;

      // Load current theme from configuration
      const config = await window.electron.ipcRenderer.invoke('get-config');
      if (config?.theme) {
        await applyTheme(config.theme);
      }

      // Listen for theme changes from main process
      window.electron.ipcRenderer.on('theme-changed', (_, theme: ThemeConfig) => {
        applyTheme(theme);
      });

      // Detect system theme preference
      detectSystemTheme();
      
      // Listen for system theme changes
      if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', detectSystemTheme);
      }
    } catch (error) {
      console.error('Failed to initialize theme system:', error);
    }
  };

  /**
   * Apply theme configuration
   */
  const applyTheme = async (theme: ThemeConfig) => {
    try {
      // Update theme state
      themeState.value.currentTheme = theme.name;
      themeState.value.isDark = theme.name.includes('dark') || theme.name.includes('cyberpunk');
      themeState.value.isHighContrast = theme.name.includes('high-contrast');

      // Apply CSS custom properties
      const root = document.documentElement;
      
      // Colors
      Object.entries(theme.colors).forEach(([key, value]) => {
        root.style.setProperty(`--color-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`, value);
      });

      // Typography
      root.style.setProperty('--font-family-primary', theme.typography.fontFamily);
      
      Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
        root.style.setProperty(`--font-size-${key}`, value);
      });
      
      Object.entries(theme.typography.fontWeight).forEach(([key, value]) => {
        root.style.setProperty(`--font-weight-${key}`, value.toString());
      });

      // Spacing
      Object.entries(theme.spacing).forEach(([key, value]) => {
        root.style.setProperty(`--spacing-${key}`, value);
      });

      // Border radius
      Object.entries(theme.borderRadius).forEach(([key, value]) => {
        root.style.setProperty(`--radius-${key}`, value);
      });

      // Shadows
      Object.entries(theme.shadows).forEach(([key, value]) => {
        root.style.setProperty(`--shadow-${key}`, value);
      });

      // Update data attributes for theme-specific styles
      root.setAttribute('data-theme', themeState.value.isDark ? 'dark' : 'light');
      
      if (themeState.value.isHighContrast) {
        root.setAttribute('data-contrast', 'high');
      } else {
        root.removeAttribute('data-contrast');
      }

      // Notify main process about theme change
      await window.electron.ipcRenderer.invoke('update-config', {
        theme: theme
      });
    } catch (error) {
      console.error('Failed to apply theme:', error);
    }
  };

  /**
   * Switch to a predefined theme
   */
  const switchTheme = async (themeName: string) => {
    const theme = themeState.value.availableThemes[themeName];
    if (theme) {
      await applyTheme(theme);
    }
  };

  /**
   * Create and apply custom theme
   */
  const createCustomTheme = async (customTheme: Partial<ThemeConfig>) => {
    try {
      const baseTheme = themeState.value.availableThemes[themeState.value.currentTheme];
      const mergedTheme: ThemeConfig = {
        ...baseTheme,
        ...customTheme,
        name: customTheme.name || 'Custom Theme',
        colors: { ...baseTheme.colors, ...customTheme.colors },
        typography: { 
          ...baseTheme.typography, 
          ...customTheme.typography,
          fontSize: { ...baseTheme.typography.fontSize, ...customTheme.typography?.fontSize },
          fontWeight: { ...baseTheme.typography.fontWeight, ...customTheme.typography?.fontWeight }
        },
        spacing: { ...baseTheme.spacing, ...customTheme.spacing },
        borderRadius: { ...baseTheme.borderRadius, ...customTheme.borderRadius },
        shadows: { ...baseTheme.shadows, ...customTheme.shadows }
      };

      themeState.value.customTheme = mergedTheme;
      await applyTheme(mergedTheme);
    } catch (error) {
      console.error('Failed to create custom theme:', error);
    }
  };

  /**
   * Export current theme configuration
   */
  const exportTheme = async (): Promise<ThemeConfig | null> => {
    try {
      const config = await window.electron.ipcRenderer.invoke('get-config');
      return config?.theme || null;
    } catch (error) {
      console.error('Failed to export theme:', error);
      return null;
    }
  };

  /**
   * Import theme configuration
   */
  const importTheme = async (theme: ThemeConfig) => {
    try {
      await applyTheme(theme);
      themeState.value.availableThemes[theme.name] = theme;
    } catch (error) {
      console.error('Failed to import theme:', error);
    }
  };

  /**
   * Toggle between light and dark theme
   */
  const toggleDarkMode = async () => {
    const currentTheme = themeState.value.availableThemes[themeState.value.currentTheme];
    if (!currentTheme) return;

    let targetTheme: string;
    
    if (themeState.value.isDark) {
      // Switch to light variant
      targetTheme = themeState.value.currentTheme.replace('dark', 'light');
      if (!themeState.value.availableThemes[targetTheme]) {
        targetTheme = 'modern-light';
      }
    } else {
      // Switch to dark variant
      targetTheme = themeState.value.currentTheme.replace('light', 'dark');
      if (!themeState.value.availableThemes[targetTheme]) {
        targetTheme = 'modern-dark';
      }
    }

    await switchTheme(targetTheme);
  };

  /**
   * Detect system theme preference
   */
  const detectSystemTheme = () => {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      if (!themeState.value.isDark) {
        toggleDarkMode();
      }
    } else {
      if (themeState.value.isDark) {
        toggleDarkMode();
      }
    }
  };

  /**
   * Get CSS custom property value
   */
  const getCSSVariable = (property: string): string => {
    return getComputedStyle(document.documentElement).getPropertyValue(property).trim();
  };

  /**
   * Set CSS custom property value
   */
  const setCSSVariable = (property: string, value: string) => {
    document.documentElement.style.setProperty(property, value);
  };

  /**
   * Generate theme preview
   */
  const generateThemePreview = (theme: ThemeConfig): string => {
    return `
      <div style="
        background: ${theme.colors.background};
        color: ${theme.colors.text};
        padding: 16px;
        border-radius: ${theme.borderRadius.medium};
        font-family: ${theme.typography.fontFamily};
        box-shadow: ${theme.shadows.medium};
      ">
        <div style="color: ${theme.colors.primary}; font-weight: ${theme.typography.fontWeight.bold};">
          ${theme.name}
        </div>
        <div style="color: ${theme.colors.textSecondary}; font-size: ${theme.typography.fontSize.small};">
          Preview theme
        </div>
        <div style="
          background: ${theme.colors.primary};
          color: white;
          padding: 8px 16px;
          border-radius: ${theme.borderRadius.small};
          margin-top: 8px;
          display: inline-block;
        ">
          Button
        </div>
      </div>
    `;
  };

  // Computed properties
  const currentThemeConfig = computed(() => {
    return themeState.value.customTheme || 
           themeState.value.availableThemes[themeState.value.currentTheme];
  });

  const themeList = computed(() => {
    return Object.keys(themeState.value.availableThemes);
  });

  const isDarkMode = computed(() => themeState.value.isDark);
  const isHighContrastMode = computed(() => themeState.value.isHighContrast);

  // Initialize on mount
  onMounted(() => {
    initializeTheme();
  });

  return {
    // State
    themeState: readonly(themeState),
    currentThemeConfig,
    themeList,
    isDarkMode,
    isHighContrastMode,

    // Methods
    initializeTheme,
    applyTheme,
    switchTheme,
    createCustomTheme,
    exportTheme,
    importTheme,
    toggleDarkMode,
    detectSystemTheme,
    getCSSVariable,
    setCSSVariable,
    generateThemePreview
  };
}

// Global theme utilities
export const themeUtils = {
  /**
   * Convert hex color to RGB values
   */
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },

  /**
   * Convert RGB to hex color
   */
  rgbToHex(r: number, g: number, b: number): string {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  },

  /**
   * Lighten a color by percentage
   */
  lightenColor(hex: string, percent: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const factor = 1 + percent / 100;
    const r = Math.min(255, Math.round(rgb.r * factor));
    const g = Math.min(255, Math.round(rgb.g * factor));
    const b = Math.min(255, Math.round(rgb.b * factor));

    return this.rgbToHex(r, g, b);
  },

  /**
   * Darken a color by percentage
   */
  darkenColor(hex: string, percent: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const factor = 1 - percent / 100;
    const r = Math.max(0, Math.round(rgb.r * factor));
    const g = Math.max(0, Math.round(rgb.g * factor));
    const b = Math.max(0, Math.round(rgb.b * factor));

    return this.rgbToHex(r, g, b);
  },

  /**
   * Get contrast ratio between two colors
   */
  getContrastRatio(color1: string, color2: string): number {
    const rgb1 = this.hexToRgb(color1);
    const rgb2 = this.hexToRgb(color2);
    
    if (!rgb1 || !rgb2) return 1;

    const luminance1 = this.getLuminance(rgb1.r, rgb1.g, rgb1.b);
    const luminance2 = this.getLuminance(rgb2.r, rgb2.g, rgb2.b);

    const brightest = Math.max(luminance1, luminance2);
    const darkest = Math.min(luminance1, luminance2);

    return (brightest + 0.05) / (darkest + 0.05);
  },

  /**
   * Get relative luminance of a color
   */
  getLuminance(r: number, g: number, b: number): number {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }
};
