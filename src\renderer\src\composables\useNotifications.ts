import { ref, reactive } from 'vue';

/**
 * Notification system composable
 * Handles in-app notifications and toast messages
 */

export interface Notification {
  id: string;
  title: string;
  message?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  persistent?: boolean;
  actions?: NotificationAction[];
  timestamp: Date;
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

const notifications = ref<Notification[]>([]);
const maxNotifications = 5;
const defaultDuration = 5000; // 5 seconds

export function useNotifications() {
  /**
   * Generate unique notification ID
   */
  const generateId = (): string => {
    return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  };

  /**
   * Add a new notification
   */
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>): string => {
    const id = generateId();
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date(),
      duration: notification.duration ?? defaultDuration
    };

    notifications.value.unshift(newNotification);

    // Limit the number of notifications
    if (notifications.value.length > maxNotifications) {
      notifications.value = notifications.value.slice(0, maxNotifications);
    }

    // Auto-remove notification after duration (if not persistent)
    if (!newNotification.persistent && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  };

  /**
   * Remove a notification by ID
   */
  const removeNotification = (id: string): void => {
    const index = notifications.value.findIndex(n => n.id === id);
    if (index > -1) {
      notifications.value.splice(index, 1);
    }
  };

  /**
   * Clear all notifications
   */
  const clearAllNotifications = (): void => {
    notifications.value = [];
  };

  /**
   * Show a simple notification
   */
  const showNotification = (
    title: string,
    type: Notification['type'] = 'info',
    options?: {
      message?: string;
      duration?: number;
      persistent?: boolean;
      actions?: NotificationAction[];
    }
  ): string => {
    return addNotification({
      title,
      type,
      message: options?.message,
      duration: options?.duration,
      persistent: options?.persistent,
      actions: options?.actions
    });
  };

  /**
   * Show success notification
   */
  const showSuccess = (title: string, message?: string, duration?: number): string => {
    return showNotification(title, 'success', { message, duration });
  };

  /**
   * Show error notification
   */
  const showError = (title: string, message?: string, persistent = true): string => {
    return showNotification(title, 'error', { message, persistent });
  };

  /**
   * Show warning notification
   */
  const showWarning = (title: string, message?: string, duration?: number): string => {
    return showNotification(title, 'warning', { message, duration });
  };

  /**
   * Show info notification
   */
  const showInfo = (title: string, message?: string, duration?: number): string => {
    return showNotification(title, 'info', { message, duration });
  };

  /**
   * Show confirmation notification with actions
   */
  const showConfirmation = (
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ): string => {
    const actions: NotificationAction[] = [
      {
        label: 'Confirm',
        action: () => {
          onConfirm();
          // The notification will be removed when the action is executed
        },
        style: 'primary'
      }
    ];

    if (onCancel) {
      actions.push({
        label: 'Cancel',
        action: () => {
          onCancel();
        },
        style: 'secondary'
      });
    }

    return showNotification(title, 'warning', {
      message,
      persistent: true,
      actions
    });
  };

  /**
   * Execute notification action and remove notification
   */
  const executeAction = (notificationId: string, action: NotificationAction): void => {
    try {
      action.action();
    } catch (error) {
      console.error('Error executing notification action:', error);
      showError('Action failed', 'An error occurred while executing the action');
    } finally {
      removeNotification(notificationId);
    }
  };

  /**
   * Get notification by ID
   */
  const getNotification = (id: string): Notification | undefined => {
    return notifications.value.find(n => n.id === id);
  };

  /**
   * Get notifications by type
   */
  const getNotificationsByType = (type: Notification['type']): Notification[] => {
    return notifications.value.filter(n => n.type === type);
  };

  /**
   * Check if there are any error notifications
   */
  const hasErrors = (): boolean => {
    return notifications.value.some(n => n.type === 'error');
  };

  /**
   * Check if there are any warning notifications
   */
  const hasWarnings = (): boolean => {
    return notifications.value.some(n => n.type === 'warning');
  };

  /**
   * Get notification count by type
   */
  const getNotificationCount = (type?: Notification['type']): number => {
    if (type) {
      return notifications.value.filter(n => n.type === type).length;
    }
    return notifications.value.length;
  };

  /**
   * Mark notification as read (for future use)
   */
  const markAsRead = (id: string): void => {
    const notification = getNotification(id);
    if (notification) {
      // Add read property in the future if needed
      console.log(`Notification ${id} marked as read`);
    }
  };

  return {
    // State
    notifications: readonly(notifications),

    // Methods
    addNotification,
    removeNotification,
    clearAllNotifications,
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirmation,
    executeAction,
    getNotification,
    getNotificationsByType,
    hasErrors,
    hasWarnings,
    getNotificationCount,
    markAsRead
  };
}

// Global notification state
const globalNotificationState = useNotifications();

export const useGlobalNotifications = () => globalNotificationState;
