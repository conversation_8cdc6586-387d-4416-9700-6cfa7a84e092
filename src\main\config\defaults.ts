import { AppConfig } from './types';

/**
 * Default configuration values
 */
export const defaultConfig: AppConfig = {
  version: '1.0.0',
  theme: {
    name: 'Modern Dark',
    colors: {
      primary: '#6366f1',
      secondary: '#8b5cf6',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f8fafc',
      textSecondary: '#cbd5e1',
      accent: '#06b6d4',
      error: '#ef4444',
      warning: '#f59e0b',
      success: '#10b981'
    },
    typography: {
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: {
        small: '0.875rem',
        medium: '1rem',
        large: '1.125rem',
        xlarge: '1.25rem'
      },
      fontWeight: {
        light: 300,
        normal: 400,
        medium: 500,
        bold: 700
      }
    },
    spacing: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem'
    },
    borderRadius: {
      small: '0.25rem',
      medium: '0.5rem',
      large: '0.75rem'
    },
    shadows: {
      small: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      medium: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      large: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
    }
  },
  window: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    alwaysOnTop: false,
    skipTaskbar: false,
    titleBarStyle: 'default',
    frame: true,
    transparent: false,
    opacity: 1.0,
    vibrancy: null,
    backgroundMaterial: null
  },
  startup: {
    autoStart: false,
    startMinimized: false,
    startInTray: false,
    singleInstance: true,
    openAtLogin: false,
    openAsHidden: false
  },
  tray: {
    enabled: true,
    minimizeToTray: false,
    closeToTray: false,
    showNotifications: true,
    tooltip: 'Electron App'
  },
  registry: {
    enabled: false,
    backupBeforeChanges: true,
    maxBackups: 10,
    allowedKeys: [
      'HKEY_CURRENT_USER\\Software\\MyApp'
    ],
    blockedKeys: [
      'HKEY_LOCAL_MACHINE\\SYSTEM',
      'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run'
    ],
    requireConfirmation: true
  },
  general: {
    language: 'en',
    autoUpdate: true,
    telemetry: false,
    debugMode: false
  }
};

/**
 * Predefined theme configurations
 */
export const predefinedThemes = {
  'modern-dark': {
    ...defaultConfig.theme,
    name: 'Modern Dark'
  },
  'modern-light': {
    ...defaultConfig.theme,
    name: 'Modern Light',
    colors: {
      primary: '#6366f1',
      secondary: '#8b5cf6',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#0f172a',
      textSecondary: '#475569',
      accent: '#06b6d4',
      error: '#ef4444',
      warning: '#f59e0b',
      success: '#10b981'
    }
  },
  'cyberpunk': {
    ...defaultConfig.theme,
    name: 'Cyberpunk',
    colors: {
      primary: '#ff0080',
      secondary: '#00ff80',
      background: '#0a0a0a',
      surface: '#1a1a1a',
      text: '#00ff80',
      textSecondary: '#80ff80',
      accent: '#ff8000',
      error: '#ff0040',
      warning: '#ffff00',
      success: '#00ff40'
    }
  },
  'ocean': {
    ...defaultConfig.theme,
    name: 'Ocean',
    colors: {
      primary: '#0ea5e9',
      secondary: '#06b6d4',
      background: '#0c4a6e',
      surface: '#075985',
      text: '#e0f2fe',
      textSecondary: '#bae6fd',
      accent: '#22d3ee',
      error: '#f87171',
      warning: '#fbbf24',
      success: '#34d399'
    }
  }
};
