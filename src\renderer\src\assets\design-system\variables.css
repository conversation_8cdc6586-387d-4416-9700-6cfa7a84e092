/**
 * Modern Design System - CSS Custom Properties
 * Centralized design tokens for consistent theming
 */

:root {
  /* Color Palette */
  --color-primary: #6366f1;
  --color-primary-hover: #5855eb;
  --color-primary-active: #4f46e5;
  --color-primary-light: #a5b4fc;
  --color-primary-dark: #3730a3;

  --color-secondary: #8b5cf6;
  --color-secondary-hover: #7c3aed;
  --color-secondary-active: #6d28d9;
  --color-secondary-light: #c4b5fd;
  --color-secondary-dark: #5b21b6;

  --color-accent: #06b6d4;
  --color-accent-hover: #0891b2;
  --color-accent-active: #0e7490;
  --color-accent-light: #67e8f9;
  --color-accent-dark: #164e63;

  /* Status Colors */
  --color-success: #10b981;
  --color-success-hover: #059669;
  --color-success-light: #6ee7b7;
  --color-success-dark: #047857;

  --color-warning: #f59e0b;
  --color-warning-hover: #d97706;
  --color-warning-light: #fcd34d;
  --color-warning-dark: #92400e;

  --color-error: #ef4444;
  --color-error-hover: #dc2626;
  --color-error-light: #fca5a5;
  --color-error-dark: #991b1b;

  --color-info: #3b82f6;
  --color-info-hover: #2563eb;
  --color-info-light: #93c5fd;
  --color-info-dark: #1d4ed8;

  /* Neutral Colors */
  --color-white: #ffffff;
  --color-black: #000000;
  
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Dark Theme Colors */
  --color-dark-50: #0f172a;
  --color-dark-100: #1e293b;
  --color-dark-200: #334155;
  --color-dark-300: #475569;
  --color-dark-400: #64748b;
  --color-dark-500: #94a3b8;
  --color-dark-600: #cbd5e1;
  --color-dark-700: #e2e8f0;
  --color-dark-800: #f1f5f9;
  --color-dark-900: #f8fafc;

  /* Semantic Colors - Light Theme */
  --color-background: var(--color-white);
  --color-background-secondary: var(--color-gray-50);
  --color-background-tertiary: var(--color-gray-100);
  
  --color-surface: var(--color-white);
  --color-surface-secondary: var(--color-gray-50);
  --color-surface-tertiary: var(--color-gray-100);
  
  --color-text: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-600);
  --color-text-tertiary: var(--color-gray-500);
  --color-text-inverse: var(--color-white);
  
  --color-border: var(--color-gray-200);
  --color-border-secondary: var(--color-gray-300);
  --color-border-focus: var(--color-primary);

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-secondary: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  --spacing-24: 6rem;     /* 96px */
  --spacing-32: 8rem;     /* 128px */

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Z-Index */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  --transition-colors: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
  --transition-opacity: opacity var(--transition-fast);
  --transition-transform: transform var(--transition-fast);
  --transition-all: all var(--transition-base);

  /* Breakpoints (for use in media queries) */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Component Specific */
  --button-height-sm: 2rem;
  --button-height-base: 2.5rem;
  --button-height-lg: 3rem;
  
  --input-height-sm: 2rem;
  --input-height-base: 2.5rem;
  --input-height-lg: 3rem;
  
  --sidebar-width: 16rem;
  --header-height: 4rem;
  --footer-height: 3rem;

  /* Animation Curves */
  --ease-in-quad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  --ease-in-cubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
  --ease-in-out-quad: cubic-bezier(0.455, 0.03, 0.515, 0.955);
  --ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* Dark Theme Override */
[data-theme="dark"] {
  --color-background: var(--color-dark-50);
  --color-background-secondary: var(--color-dark-100);
  --color-background-tertiary: var(--color-dark-200);
  
  --color-surface: var(--color-dark-100);
  --color-surface-secondary: var(--color-dark-200);
  --color-surface-tertiary: var(--color-dark-300);
  
  --color-text: var(--color-dark-900);
  --color-text-secondary: var(--color-dark-600);
  --color-text-tertiary: var(--color-dark-500);
  --color-text-inverse: var(--color-dark-50);
  
  --color-border: var(--color-dark-300);
  --color-border-secondary: var(--color-dark-400);
  
  /* Adjust shadows for dark theme */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
}

/* High Contrast Theme */
[data-theme="high-contrast"] {
  --color-primary: #0000ff;
  --color-background: #ffffff;
  --color-text: #000000;
  --color-border: #000000;
  
  /* Remove shadows for high contrast */
  --shadow-sm: none;
  --shadow-base: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;
  --shadow-2xl: none;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0ms;
    --transition-base: 0ms;
    --transition-slow: 0ms;
  }
}
