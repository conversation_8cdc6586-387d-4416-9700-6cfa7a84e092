<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useTheme } from './composables/useTheme'
import SettingsPanel from './components/Settings/SettingsPanel.vue'
import Versions from './components/Versions.vue'

const { initializeTheme } = useTheme()
const showSettings = ref(false)

const ipcHandle = (): void => window.electron.ipcRenderer.send('ping')

const openSettings = () => {
  showSettings.value = true
}

const closeSettings = () => {
  showSettings.value = false
}

onMounted(async () => {
  await initializeTheme()

  // Listen for settings open command from main process
  window.electron.ipcRenderer.on('open-settings', () => {
    openSettings()
  })
})
</script>

<template>
  <div id="app" class="app-container">
    <!-- Main Application Content -->
    <div v-if="!showSettings" class="main-content">
      <img alt="logo" class="logo" src="./assets/electron.svg" />
      <div class="creator">Modern Electron App</div>
      <div class="text">
        Build with
        <span class="vue">Vue 3</span>
        and
        <span class="ts">TypeScript</span>
      </div>
      <p class="tip">A modern, themeable Electron application with advanced features</p>

      <div class="features-grid">
        <div class="feature-card">
          <h3>🎨 Advanced Theming</h3>
          <p>Customizable themes with dark mode support</p>
        </div>
        <div class="feature-card">
          <h3>⚙️ Registry Management</h3>
          <p>Safe Windows Registry operations with backups</p>
        </div>
        <div class="feature-card">
          <h3>🚀 Auto Startup</h3>
          <p>System tray integration and startup management</p>
        </div>
        <div class="feature-card">
          <h3>📁 Configuration</h3>
          <p>Centralized settings with import/export</p>
        </div>
      </div>

      <div class="actions">
        <button @click="openSettings" class="btn btn-primary">
          Open Settings
        </button>
        <button @click="ipcHandle" class="btn btn-secondary">
          Test IPC
        </button>
      </div>

      <Versions />
    </div>

    <!-- Settings Panel -->
    <div v-if="showSettings" class="settings-overlay">
      <div class="settings-container">
        <div class="settings-header">
          <button @click="closeSettings" class="close-button">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <SettingsPanel />
      </div>
    </div>
  </div>
</template>
