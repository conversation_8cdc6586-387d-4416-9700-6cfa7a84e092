/**
 * Windows Registry management types and interfaces
 */

export type RegistryHive = 
  | 'HKEY_CLASSES_ROOT' 
  | 'HKEY_CURRENT_USER' 
  | 'HKEY_LOCAL_MACHINE' 
  | 'HKEY_USERS' 
  | 'HKEY_CURRENT_CONFIG';

export type RegistryValueType = 
  | 'REG_SZ' 
  | 'REG_DWORD' 
  | 'REG_QWORD' 
  | 'REG_BINARY' 
  | 'REG_MULTI_SZ' 
  | 'REG_EXPAND_SZ';

export interface RegistryValue {
  name: string;
  type: RegistryValueType;
  value: string | number | Buffer | string[];
}

export interface RegistryKey {
  hive: RegistryHive;
  key: string;
  values?: RegistryValue[];
  subKeys?: string[];
}

export interface RegistryOperation {
  id: string;
  timestamp: string;
  type: 'read' | 'write' | 'delete' | 'create';
  hive: RegistryHive;
  key: string;
  valueName?: string;
  oldValue?: any;
  newValue?: any;
  success: boolean;
  error?: string;
}

export interface RegistryBackup {
  id: string;
  timestamp: string;
  description: string;
  operations: RegistryOperation[];
  keys: RegistryKey[];
}

export interface RegistryPermission {
  hive: RegistryHive;
  keyPattern: string;
  allowed: boolean;
  reason?: string;
}

export interface RegistryValidationRule {
  keyPattern: string;
  valuePattern?: string;
  allowedTypes?: RegistryValueType[];
  validator?: (value: any) => boolean;
  message?: string;
}

export interface RegistryManagerOptions {
  enableBackups: boolean;
  maxBackups: number;
  requireConfirmation: boolean;
  allowedKeys: string[];
  blockedKeys: string[];
  validationRules: RegistryValidationRule[];
}

export class RegistryError extends Error {
  constructor(
    message: string,
    public code: string,
    public hive?: RegistryHive,
    public key?: string,
    public valueName?: string
  ) {
    super(message);
    this.name = 'RegistryError';
  }
}

export class RegistryPermissionError extends RegistryError {
  constructor(hive: RegistryHive, key: string, reason: string) {
    super(`Access denied to ${hive}\\${key}: ${reason}`, 'PERMISSION_DENIED', hive, key);
    this.name = 'RegistryPermissionError';
  }
}

export class RegistryValidationError extends RegistryError {
  constructor(message: string, hive: RegistryHive, key: string, valueName?: string) {
    super(message, 'VALIDATION_ERROR', hive, key, valueName);
    this.name = 'RegistryValidationError';
  }
}
