# Electron App Modernization Guide

## Overview

This guide documents the comprehensive modernization of the Electron application with advanced features including theming, Windows Registry management, application lifecycle management, and a modern UI design system.

## 🎨 Features Implemented

### 1. Modern UI Design System
- **CSS Custom Properties**: Centralized design tokens for consistent theming
- **Component Library**: Reusable UI components with modern styling
- **Responsive Design**: Mobile-first approach with breakpoint-based layouts
- **CSS Grid & Flexbox**: Modern layout techniques for complex UIs
- **Smooth Animations**: CSS transitions and keyframe animations
- **Accessibility**: Focus management and high contrast support

### 2. Advanced Theming System
- **Predefined Themes**: Multiple built-in themes (Modern Dark, Modern Light, Cyberpunk, Ocean)
- **Custom Theme Builder**: Visual theme editor with color picker and typography controls
- **System Theme Detection**: Automatic theme switching based on OS preference
- **Theme Import/Export**: Save and share custom themes
- **Real-time Preview**: Live theme changes without restart
- **CSS Variable Integration**: Dynamic theme application using CSS custom properties

### 3. Configuration Management
- **Centralized Config**: YAML-based configuration with JSON schema validation
- **Type Safety**: Full TypeScript support for configuration objects
- **Backup System**: Automatic configuration backups with restore functionality
- **Import/Export**: Configuration profiles for easy sharing
- **Validation**: Schema-based validation with error reporting
- **Hot Reload**: Configuration changes applied without restart

### 4. Windows Registry Management
- **Safe Operations**: Permission-based registry access with validation
- **Backup/Restore**: Automatic backups before registry modifications
- **Error Handling**: Comprehensive error handling and user feedback
- **Validation Rules**: Configurable validation for registry operations
- **Operation Logging**: Detailed logs of all registry operations
- **Permission System**: Whitelist/blacklist for registry keys

### 5. Application Lifecycle Management
- **Auto-startup**: Windows startup integration with configuration options
- **Single Instance**: Prevent multiple app instances
- **System Tray**: Minimize to tray with context menu
- **Window Management**: Configurable window behavior and appearance
- **Startup Behavior**: Customizable startup modes (normal, minimized, tray)
- **Process Management**: Proper cleanup and resource management

## 📁 Project Structure

```
src/
├── main/
│   ├── config/
│   │   ├── types.ts          # Configuration type definitions
│   │   ├── defaults.ts       # Default configuration values
│   │   ├── schema.ts         # JSON schema for validation
│   │   └── manager.ts        # Configuration manager class
│   ├── registry/
│   │   ├── types.ts          # Registry operation types
│   │   └── manager.ts        # Registry manager class
│   ├── lifecycle/
│   │   └── manager.ts        # Application lifecycle manager
│   └── index.ts              # Main process entry point
├── renderer/src/
│   ├── assets/
│   │   ├── design-system/
│   │   │   ├── variables.css # CSS custom properties
│   │   │   └── components.css # Component styles
│   │   └── main.css          # Main stylesheet
│   ├── components/
│   │   └── Settings/
│   │       ├── SettingsPanel.vue    # Main settings interface
│   │       └── ThemeSettings.vue    # Theme configuration UI
│   ├── composables/
│   │   ├── useTheme.ts       # Theme management composable
│   │   ├── useConfig.ts      # Configuration composable
│   │   └── useNotifications.ts # Notification system
│   └── App.vue               # Main application component
└── preload/
    ├── index.ts              # Preload script with API exposure
    └── index.d.ts            # Type definitions for preload APIs
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) or npm
- Windows 10/11 (for registry features)

### Installation
```bash
# Install dependencies
pnpm install

# Development
pnpm run dev

# Build for production
pnpm run build:win
```

### Configuration
The application uses a YAML configuration file located at:
```
%APPDATA%/electron-app/config.yaml
```

## 🎨 Theming System

### Using Predefined Themes
```typescript
import { useTheme } from '@/composables/useTheme'

const { switchTheme, availableThemes } = useTheme()

// Switch to a predefined theme
await switchTheme('modern-dark')
```

### Creating Custom Themes
```typescript
const customTheme = {
  name: 'My Custom Theme',
  colors: {
    primary: '#ff6b6b',
    secondary: '#4ecdc4',
    background: '#ffffff',
    // ... other colors
  },
  typography: {
    fontFamily: 'Roboto, sans-serif',
    fontSize: {
      small: '0.875rem',
      medium: '1rem',
      // ... other sizes
    }
  }
}

await createCustomTheme(customTheme)
```

### CSS Custom Properties
The theming system uses CSS custom properties that can be accessed in your styles:

```css
.my-component {
  background-color: var(--color-primary);
  color: var(--color-text);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-colors);
}
```

## ⚙️ Configuration Management

### Loading Configuration
```typescript
import { useConfig } from '@/composables/useConfig'

const { config, loadConfig, updateConfig } = useConfig()

// Load configuration
await loadConfig()

// Update configuration
await updateConfig({
  theme: { name: 'dark-theme' },
  window: { width: 1200, height: 800 }
})
```

### Configuration Schema
The configuration follows a strict TypeScript interface:

```typescript
interface AppConfig {
  version: string
  theme: ThemeConfig
  window: WindowConfig
  startup: StartupConfig
  tray: TrayConfig
  registry: RegistryConfig
  general: GeneralConfig
}
```

## 🗃️ Registry Management

### Safe Registry Operations
```typescript
import { RegistryManager } from '@/main/registry/manager'

const registryManager = new RegistryManager({
  enableBackups: true,
  requireConfirmation: true,
  allowedKeys: ['HKEY_CURRENT_USER\\Software\\MyApp'],
  blockedKeys: ['HKEY_LOCAL_MACHINE\\SYSTEM']
})

// Read a registry value
const value = await registryManager.readValue(
  'HKEY_CURRENT_USER',
  'Software\\MyApp',
  'Setting1'
)

// Write a registry value (with backup)
await registryManager.writeValue(
  'HKEY_CURRENT_USER',
  'Software\\MyApp',
  'Setting1',
  'NewValue',
  'REG_SZ'
)
```

## 🔄 Application Lifecycle

### Auto-startup Configuration
```typescript
import { lifecycleManager } from '@/main/lifecycle/manager'

// Enable auto-startup
await lifecycleManager.toggleAutoStartup()

// Check auto-startup status
const isEnabled = await lifecycleManager.isAutoStartupEnabled()
```

### System Tray Integration
The application automatically creates a system tray icon when enabled in configuration:

```yaml
tray:
  enabled: true
  minimizeToTray: true
  closeToTray: false
  showNotifications: true
  tooltip: "My Electron App"
```

## 🎯 Best Practices

### Theme Development
1. Use CSS custom properties for all theme-related values
2. Test themes in both light and dark modes
3. Ensure sufficient color contrast for accessibility
4. Use semantic color names (primary, secondary, etc.)

### Configuration Management
1. Always validate configuration before applying
2. Create backups before making changes
3. Use TypeScript interfaces for type safety
4. Handle errors gracefully with user feedback

### Registry Operations
1. Always use the permission system
2. Create backups before modifications
3. Validate registry keys and values
4. Provide clear user feedback for operations

## 🧪 Testing

### Running Tests
```bash
# Unit tests
pnpm run test

# E2E tests
pnpm run test:e2e

# Type checking
pnpm run typecheck
```

### Test Structure
- Unit tests for configuration management
- Integration tests for registry operations
- E2E tests for UI interactions
- Theme system validation tests

## 📦 Building and Distribution

### Development Build
```bash
pnpm run dev
```

### Production Build
```bash
# Windows
pnpm run build:win

# macOS
pnpm run build:mac

# Linux
pnpm run build:linux
```

### Distribution
The application is packaged using electron-builder with the following features:
- Auto-updater integration
- Code signing (configure in electron-builder.yml)
- NSIS installer for Windows
- DMG for macOS
- AppImage for Linux

## 🔧 Troubleshooting

### Common Issues

1. **Theme not applying**: Check CSS custom property names and ensure theme is properly loaded
2. **Registry access denied**: Verify permissions and allowed/blocked key configurations
3. **Auto-startup not working**: Check Windows UAC settings and application permissions
4. **Configuration not saving**: Verify file permissions in user data directory

### Debug Mode
Enable debug mode in configuration:
```yaml
general:
  debugMode: true
```

This enables:
- Detailed console logging
- Development tools access
- Extended error messages
- Performance monitoring

## 📚 API Reference

### Theme API
- `useTheme()` - Theme management composable
- `switchTheme(name)` - Switch to predefined theme
- `createCustomTheme(theme)` - Create custom theme
- `exportTheme()` - Export current theme
- `importTheme(theme)` - Import theme configuration

### Configuration API
- `useConfig()` - Configuration management composable
- `loadConfig()` - Load configuration from file
- `updateConfig(updates)` - Update configuration
- `resetConfig()` - Reset to defaults
- `exportConfig(path)` - Export configuration
- `importConfig(path)` - Import configuration

### Registry API
- `RegistryManager` - Registry operations class
- `readValue()` - Read registry value
- `writeValue()` - Write registry value
- `deleteValue()` - Delete registry value
- `createBackup()` - Create registry backup
- `restoreFromBackup()` - Restore from backup

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
