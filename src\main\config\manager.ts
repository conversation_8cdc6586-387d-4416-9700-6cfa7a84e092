import { app } from 'electron';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as yaml from 'yaml';
import Ajv from 'ajv';
import { AppConfig, ConfigValidationError, ConfigBackup } from './types';
import { defaultConfig } from './defaults';
import { configSchema } from './schema';

/**
 * Configuration Manager for the Electron application
 * Handles loading, saving, validation, and backup of configuration
 */
export class ConfigManager {
  private config: AppConfig;
  private configPath: string;
  private backupDir: string;
  private ajv: Ajv;
  private watchers: Map<string, (config: AppConfig) => void> = new Map();

  constructor() {
    this.config = { ...defaultConfig };
    this.configPath = path.join(app.getPath('userData'), 'config.yaml');
    this.backupDir = path.join(app.getPath('userData'), 'config-backups');
    this.ajv = new Ajv({ allErrors: true });
  }

  /**
   * Initialize the configuration manager
   */
  async initialize(): Promise<void> {
    try {
      // Ensure backup directory exists
      await fs.mkdir(this.backupDir, { recursive: true });
      
      // Load existing configuration or create default
      await this.load();
      
      // Clean old backups
      await this.cleanOldBackups();
    } catch (error) {
      console.error('Failed to initialize configuration manager:', error);
      throw error;
    }
  }

  /**
   * Load configuration from file
   */
  async load(): Promise<void> {
    try {
      const configExists = await fs.access(this.configPath).then(() => true).catch(() => false);
      
      if (configExists) {
        const configData = await fs.readFile(this.configPath, 'utf-8');
        const parsedConfig = yaml.parse(configData);
        
        // Validate configuration
        const validationResult = this.validate(parsedConfig);
        if (validationResult.length > 0) {
          console.warn('Configuration validation errors:', validationResult);
          // Use default config with warning
          this.config = { ...defaultConfig };
          await this.save(); // Save corrected config
        } else {
          this.config = parsedConfig;
        }
      } else {
        // Create default configuration file
        this.config = { ...defaultConfig };
        await this.save();
      }
    } catch (error) {
      console.error('Failed to load configuration:', error);
      this.config = { ...defaultConfig };
      await this.save();
    }
  }

  /**
   * Save configuration to file
   */
  async save(): Promise<void> {
    try {
      // Create backup before saving
      if (await fs.access(this.configPath).then(() => true).catch(() => false)) {
        await this.createBackup('Auto backup before save');
      }

      const configYaml = yaml.stringify(this.config, {
        indent: 2,
        lineWidth: 120,
        minContentWidth: 20
      });

      await fs.writeFile(this.configPath, configYaml, 'utf-8');
      
      // Notify watchers
      this.notifyWatchers();
    } catch (error) {
      console.error('Failed to save configuration:', error);
      throw error;
    }
  }

  /**
   * Validate configuration against schema
   */
  validate(config: any): ConfigValidationError[] {
    const validate = this.ajv.compile(configSchema);
    const valid = validate(config);
    
    if (!valid && validate.errors) {
      return validate.errors.map(error => ({
        path: error.instancePath || error.schemaPath,
        message: error.message || 'Unknown validation error',
        value: error.data
      }));
    }
    
    return [];
  }

  /**
   * Get current configuration
   */
  getConfig(): AppConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  async updateConfig(updates: Partial<AppConfig>): Promise<void> {
    const newConfig = this.mergeConfig(this.config, updates);
    
    // Validate new configuration
    const validationErrors = this.validate(newConfig);
    if (validationErrors.length > 0) {
      throw new Error(`Configuration validation failed: ${validationErrors.map(e => e.message).join(', ')}`);
    }
    
    this.config = newConfig;
    await this.save();
  }

  /**
   * Reset configuration to defaults
   */
  async resetToDefaults(): Promise<void> {
    await this.createBackup('Reset to defaults');
    this.config = { ...defaultConfig };
    await this.save();
  }

  /**
   * Export configuration to file
   */
  async exportConfig(filePath: string, format: 'json' | 'yaml' = 'yaml'): Promise<void> {
    try {
      let content: string;
      
      if (format === 'json') {
        content = JSON.stringify(this.config, null, 2);
      } else {
        content = yaml.stringify(this.config, { indent: 2 });
      }
      
      await fs.writeFile(filePath, content, 'utf-8');
    } catch (error) {
      console.error('Failed to export configuration:', error);
      throw error;
    }
  }

  /**
   * Import configuration from file
   */
  async importConfig(filePath: string): Promise<void> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      let importedConfig: any;
      
      if (filePath.endsWith('.json')) {
        importedConfig = JSON.parse(content);
      } else {
        importedConfig = yaml.parse(content);
      }
      
      // Validate imported configuration
      const validationErrors = this.validate(importedConfig);
      if (validationErrors.length > 0) {
        throw new Error(`Invalid configuration: ${validationErrors.map(e => e.message).join(', ')}`);
      }
      
      // Create backup before import
      await this.createBackup('Before import');
      
      this.config = importedConfig;
      await this.save();
    } catch (error) {
      console.error('Failed to import configuration:', error);
      throw error;
    }
  }

  /**
   * Create configuration backup
   */
  async createBackup(description?: string): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `config-backup-${timestamp}.yaml`;
      const backupPath = path.join(this.backupDir, backupFileName);
      
      const backup: ConfigBackup = {
        timestamp: new Date().toISOString(),
        version: this.config.version,
        config: { ...this.config },
        description
      };
      
      const backupContent = yaml.stringify(backup, { indent: 2 });
      await fs.writeFile(backupPath, backupContent, 'utf-8');
      
      return backupPath;
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw error;
    }
  }

  /**
   * List available backups
   */
  async listBackups(): Promise<ConfigBackup[]> {
    try {
      const files = await fs.readdir(this.backupDir);
      const backupFiles = files.filter(file => file.startsWith('config-backup-') && file.endsWith('.yaml'));
      
      const backups: ConfigBackup[] = [];
      
      for (const file of backupFiles) {
        try {
          const filePath = path.join(this.backupDir, file);
          const content = await fs.readFile(filePath, 'utf-8');
          const backup = yaml.parse(content) as ConfigBackup;
          backups.push(backup);
        } catch (error) {
          console.warn(`Failed to read backup file ${file}:`, error);
        }
      }
      
      return backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    } catch (error) {
      console.error('Failed to list backups:', error);
      return [];
    }
  }

  /**
   * Restore configuration from backup
   */
  async restoreFromBackup(timestamp: string): Promise<void> {
    try {
      const backups = await this.listBackups();
      const backup = backups.find(b => b.timestamp === timestamp);
      
      if (!backup) {
        throw new Error(`Backup with timestamp ${timestamp} not found`);
      }
      
      // Validate backup configuration
      const validationErrors = this.validate(backup.config);
      if (validationErrors.length > 0) {
        throw new Error(`Backup configuration is invalid: ${validationErrors.map(e => e.message).join(', ')}`);
      }
      
      // Create backup of current config before restore
      await this.createBackup('Before restore');
      
      this.config = backup.config;
      await this.save();
    } catch (error) {
      console.error('Failed to restore from backup:', error);
      throw error;
    }
  }

  /**
   * Watch for configuration changes
   */
  watch(key: string, callback: (config: AppConfig) => void): void {
    this.watchers.set(key, callback);
  }

  /**
   * Stop watching for configuration changes
   */
  unwatch(key: string): void {
    this.watchers.delete(key);
  }

  /**
   * Clean old backups (keep only maxBackups)
   */
  private async cleanOldBackups(): Promise<void> {
    try {
      const backups = await this.listBackups();
      const maxBackups = this.config.registry.maxBackups;
      
      if (backups.length > maxBackups) {
        const backupsToDelete = backups.slice(maxBackups);
        
        for (const backup of backupsToDelete) {
          const fileName = `config-backup-${backup.timestamp.replace(/[:.]/g, '-')}.yaml`;
          const filePath = path.join(this.backupDir, fileName);
          
          try {
            await fs.unlink(filePath);
          } catch (error) {
            console.warn(`Failed to delete old backup ${fileName}:`, error);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to clean old backups:', error);
    }
  }

  /**
   * Notify all watchers of configuration changes
   */
  private notifyWatchers(): void {
    for (const callback of this.watchers.values()) {
      try {
        callback(this.getConfig());
      } catch (error) {
        console.error('Error in configuration watcher:', error);
      }
    }
  }

  /**
   * Deep merge configuration objects
   */
  private mergeConfig(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.mergeConfig(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }
}

// Singleton instance
export const configManager = new ConfigManager();
