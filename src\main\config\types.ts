/**
 * Configuration types and interfaces for the Electron application
 */

export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    accent: string;
    error: string;
    warning: string;
    success: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      small: string;
      medium: string;
      large: string;
      xlarge: string;
    };
    fontWeight: {
      light: number;
      normal: number;
      medium: number;
      bold: number;
    };
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    small: string;
    medium: string;
    large: string;
  };
  shadows: {
    small: string;
    medium: string;
    large: string;
  };
}

export interface WindowConfig {
  width: number;
  height: number;
  minWidth: number;
  minHeight: number;
  resizable: boolean;
  maximizable: boolean;
  minimizable: boolean;
  closable: boolean;
  alwaysOnTop: boolean;
  skipTaskbar: boolean;
  titleBarStyle: 'default' | 'hidden' | 'hiddenInset' | 'customButtonsOnHover';
  frame: boolean;
  transparent: boolean;
  opacity: number;
  vibrancy: 'appearance-based' | 'light' | 'dark' | 'titlebar' | 'selection' | 'menu' | 'popover' | 'sidebar' | 'medium-light' | 'ultra-dark' | null;
  backgroundMaterial: 'auto' | 'none' | 'mica' | 'acrylic' | 'tabbed' | null;
}

export interface StartupConfig {
  autoStart: boolean;
  startMinimized: boolean;
  startInTray: boolean;
  singleInstance: boolean;
  openAtLogin: boolean;
  openAsHidden: boolean;
}

export interface TrayConfig {
  enabled: boolean;
  minimizeToTray: boolean;
  closeToTray: boolean;
  showNotifications: boolean;
  tooltip: string;
}

export interface RegistryConfig {
  enabled: boolean;
  backupBeforeChanges: boolean;
  maxBackups: number;
  allowedKeys: string[];
  blockedKeys: string[];
  requireConfirmation: boolean;
}

export interface AppConfig {
  version: string;
  theme: ThemeConfig;
  window: WindowConfig;
  startup: StartupConfig;
  tray: TrayConfig;
  registry: RegistryConfig;
  general: {
    language: string;
    autoUpdate: boolean;
    telemetry: boolean;
    debugMode: boolean;
  };
}

export interface ConfigValidationError {
  path: string;
  message: string;
  value: any;
}

export interface ConfigBackup {
  timestamp: string;
  version: string;
  config: AppConfig;
  description?: string;
}
