/**
 * J<PERSON><PERSON> Schema for configuration validation
 */
export const configSchema = {
  type: 'object',
  properties: {
    version: {
      type: 'string',
      pattern: '^\\d+\\.\\d+\\.\\d+$'
    },
    theme: {
      type: 'object',
      properties: {
        name: { type: 'string', minLength: 1 },
        colors: {
          type: 'object',
          properties: {
            primary: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
            secondary: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
            background: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
            surface: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
            text: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
            textSecondary: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
            accent: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
            error: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
            warning: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
            success: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' }
          },
          required: ['primary', 'secondary', 'background', 'surface', 'text', 'textSecondary', 'accent', 'error', 'warning', 'success'],
          additionalProperties: false
        },
        typography: {
          type: 'object',
          properties: {
            fontFamily: { type: 'string', minLength: 1 },
            fontSize: {
              type: 'object',
              properties: {
                small: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' },
                medium: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' },
                large: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' },
                xlarge: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' }
              },
              required: ['small', 'medium', 'large', 'xlarge'],
              additionalProperties: false
            },
            fontWeight: {
              type: 'object',
              properties: {
                light: { type: 'number', minimum: 100, maximum: 900 },
                normal: { type: 'number', minimum: 100, maximum: 900 },
                medium: { type: 'number', minimum: 100, maximum: 900 },
                bold: { type: 'number', minimum: 100, maximum: 900 }
              },
              required: ['light', 'normal', 'medium', 'bold'],
              additionalProperties: false
            }
          },
          required: ['fontFamily', 'fontSize', 'fontWeight'],
          additionalProperties: false
        },
        spacing: {
          type: 'object',
          properties: {
            xs: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' },
            sm: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' },
            md: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' },
            lg: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' },
            xl: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' }
          },
          required: ['xs', 'sm', 'md', 'lg', 'xl'],
          additionalProperties: false
        },
        borderRadius: {
          type: 'object',
          properties: {
            small: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' },
            medium: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' },
            large: { type: 'string', pattern: '^\\d+(\\.\\d+)?(rem|px|em)$' }
          },
          required: ['small', 'medium', 'large'],
          additionalProperties: false
        },
        shadows: {
          type: 'object',
          properties: {
            small: { type: 'string' },
            medium: { type: 'string' },
            large: { type: 'string' }
          },
          required: ['small', 'medium', 'large'],
          additionalProperties: false
        }
      },
      required: ['name', 'colors', 'typography', 'spacing', 'borderRadius', 'shadows'],
      additionalProperties: false
    },
    window: {
      type: 'object',
      properties: {
        width: { type: 'number', minimum: 400, maximum: 4000 },
        height: { type: 'number', minimum: 300, maximum: 3000 },
        minWidth: { type: 'number', minimum: 200, maximum: 2000 },
        minHeight: { type: 'number', minimum: 150, maximum: 1500 },
        resizable: { type: 'boolean' },
        maximizable: { type: 'boolean' },
        minimizable: { type: 'boolean' },
        closable: { type: 'boolean' },
        alwaysOnTop: { type: 'boolean' },
        skipTaskbar: { type: 'boolean' },
        titleBarStyle: { 
          type: 'string', 
          enum: ['default', 'hidden', 'hiddenInset', 'customButtonsOnHover'] 
        },
        frame: { type: 'boolean' },
        transparent: { type: 'boolean' },
        opacity: { type: 'number', minimum: 0.1, maximum: 1.0 },
        vibrancy: { 
          type: ['string', 'null'], 
          enum: ['appearance-based', 'light', 'dark', 'titlebar', 'selection', 'menu', 'popover', 'sidebar', 'medium-light', 'ultra-dark', null] 
        },
        backgroundMaterial: { 
          type: ['string', 'null'], 
          enum: ['auto', 'none', 'mica', 'acrylic', 'tabbed', null] 
        }
      },
      required: ['width', 'height', 'minWidth', 'minHeight', 'resizable', 'maximizable', 'minimizable', 'closable'],
      additionalProperties: false
    },
    startup: {
      type: 'object',
      properties: {
        autoStart: { type: 'boolean' },
        startMinimized: { type: 'boolean' },
        startInTray: { type: 'boolean' },
        singleInstance: { type: 'boolean' },
        openAtLogin: { type: 'boolean' },
        openAsHidden: { type: 'boolean' }
      },
      required: ['autoStart', 'startMinimized', 'startInTray', 'singleInstance'],
      additionalProperties: false
    },
    tray: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean' },
        minimizeToTray: { type: 'boolean' },
        closeToTray: { type: 'boolean' },
        showNotifications: { type: 'boolean' },
        tooltip: { type: 'string', maxLength: 100 }
      },
      required: ['enabled', 'minimizeToTray', 'closeToTray', 'showNotifications', 'tooltip'],
      additionalProperties: false
    },
    registry: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean' },
        backupBeforeChanges: { type: 'boolean' },
        maxBackups: { type: 'number', minimum: 1, maximum: 100 },
        allowedKeys: { 
          type: 'array', 
          items: { type: 'string' },
          maxItems: 50
        },
        blockedKeys: { 
          type: 'array', 
          items: { type: 'string' },
          maxItems: 100
        },
        requireConfirmation: { type: 'boolean' }
      },
      required: ['enabled', 'backupBeforeChanges', 'maxBackups', 'allowedKeys', 'blockedKeys', 'requireConfirmation'],
      additionalProperties: false
    },
    general: {
      type: 'object',
      properties: {
        language: { type: 'string', pattern: '^[a-z]{2}(-[A-Z]{2})?$' },
        autoUpdate: { type: 'boolean' },
        telemetry: { type: 'boolean' },
        debugMode: { type: 'boolean' }
      },
      required: ['language', 'autoUpdate', 'telemetry', 'debugMode'],
      additionalProperties: false
    }
  },
  required: ['version', 'theme', 'window', 'startup', 'tray', 'registry', 'general'],
  additionalProperties: false
};
